@extends('layout.layout')

@section('title', 'Barang')

@section('custom_css')
	<!-- Bootstrap 5 + DataTables CSS -->
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/5.3.3/css/bootstrap.min.css">
	<link rel="stylesheet" href="https://cdn.datatables.net/2.3.2/css/dataTables.bootstrap5.min.css">
@endsection

@section('custom_js')
	<script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
	<script src="https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/5.3.3/js/bootstrap.bundle.min.js"></script>
	<script src="https://cdn.datatables.net/2.3.2/js/dataTables.min.js"></script>
	<script src="https://cdn.datatables.net/2.3.2/js/dataTables.bootstrap5.min.js"></script>

	<script>
		$(function () {
			new DataTable('#datatable', {
				pageLength: 10,
			});
		});
	</script>

@endsection

@section('page_title', 'Barang')

@section('breadcrumb')
	<li class="breadcrumb-item"><a href="/welcome">Home</a></li>
	<li class="breadcrumb-item active" aria-current="page">Barang</li>
@endsection

@section('content')
	<!-- Default box -->
	<div class="card py-4">
		<div class="table-responsive">
			<table id="datatable" class="table table-striped table-hover table-bordered w-100">
				<thead>
					<tr>
						<th>nama</th>
						<th>detail_tempat</th>
					</tr>
				</thead>
				<tbody>
					@foreach($barangs as $barang)
						<tr>
							<td>{{ $barang->nama }}</td>
							<td>{{ $barang->detail_tempat }}</td>
						</tr>
					@endforeach
				</tbody>
			</table>
		</div>
	</div>
	<!-- /.card -->
@endsection