<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Flight>
 */
class FlightFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        return [
            'name' => $this->faker->name(),
            'airline' => $this->faker->company(),
            'departure' => $this->faker->city(),
            'arrival' => $this->faker->city(),
            'duration' => $this->faker->numberBetween(1, 10) . ' hours',
            'price' => $this->faker->numberBetween(500000, 5000000),
        ];
    }
}
